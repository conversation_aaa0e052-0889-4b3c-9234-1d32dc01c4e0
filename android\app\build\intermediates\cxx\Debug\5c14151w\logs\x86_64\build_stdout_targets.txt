ninja: Entering directory `C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\.cxx\Debug\5c14151w\x86_64'
[0/2] Re-checking globbed directories...
[1/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o
[2/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o
[3/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/pagerviewJSI-generated.cpp.o
[4/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o
[5/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/pagerview-generated.cpp.o
[6/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o
[7/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o
[8/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/EventEmitters.cpp.o
[9/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o
[10/62] Building CXX object rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o
[11/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/Props.cpp.o
[12/62] Building CXX object CMakeFiles/appmodules.dir/OnLoad.cpp.o
[13/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ComponentDescriptors.cpp.o
[14/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/States.cpp.o
[15/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o
[16/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o
[17/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o
[18/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o
[19/62] Building CXX object pagerview_autolinked_build/CMakeFiles/react_codegen_pagerview.dir/react/renderer/components/pagerview/ShadowNodes.cpp.o
[20/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o
[21/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o
[22/62] Building CXX object rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o
[23/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/95d550fe1a76342acf0f910b32acf5ed/components/safeareacontext/RNCSafeAreaViewState.cpp.o
[24/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b729e7594bdc0489a288f34cf6c257c7/components/safeareacontext/EventEmitters.cpp.o
[25/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bbec020c3c445cd24022f47cab2e5813/react/renderer/components/safeareacontext/Props.cpp.o
[26/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/6f5e9a889ce05b1437b66d76e5e7cc4b/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o
[27/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/bbec020c3c445cd24022f47cab2e5813/react/renderer/components/safeareacontext/States.cpp.o
[28/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/637075661fc7df10d099d84bc95132a7/safeareacontext/safeareacontextJSI-generated.cpp.o
[29/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/93234f7e3b55529212e573ce0ffebf12/renderer/components/safeareacontext/ShadowNodes.cpp.o
[30/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/08514487bfd740f1d25d89d48926607b/source/codegen/jni/safeareacontext-generated.cpp.o
[31/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o
[32/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/64218aea3e00fa4153a8335d5549f189/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o
[33/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o
[34/62] Building CXX object safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/b729e7594bdc0489a288f34cf6c257c7/components/safeareacontext/ComponentDescriptors.cpp.o
[35/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/11faececd8d77183512524d0983633c9/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o
[36/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o
[37/62] Linking CXX shared library C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\x86_64\libreact_codegen_safeareacontext.so
[38/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ff8ae22c20ef6e4d191479f4b7fdfdae/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o
[39/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/619a170b7e242551e8af21645e19c63e/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o
[40/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o
[41/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/619a170b7e242551e8af21645e19c63e/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o
[42/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d43c4cc4ca6eb5e1f0b03ae096a1e19/codegen/jni/react/renderer/components/rnscreens/States.cpp.o
[43/62] Building CXX object CMakeFiles/appmodules.dir/C_/Users/<USER>/eclipse-workspace/club-m-app/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o
[44/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5ec7543ddd869da8cbaa3b0de2ec8a72/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o
[45/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/130616a629a32ff2b8418883c4451a2e/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o
[46/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/dd8aabcb51cd4db9f6adc60e572921e9/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o
[47/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6d43c4cc4ca6eb5e1f0b03ae096a1e19/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o
[48/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/ab78cb32376308f1452fed2f863dee37/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o
[49/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6170f051b402263e29a17be5f116edac/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o
[50/62] Building CXX object rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6170f051b402263e29a17be5f116edac/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp.o
[51/62] Linking CXX shared library C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\x86_64\libreact_codegen_rnscreens.so
[52/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o
[53/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o
[54/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o
[55/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o
[56/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/79ee79e28c672b7398b0d91bc82e40ee/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o
[57/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/2d8972c47384b76323ec3e7b13b1dd63/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:31:44: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   31 | void RNSVGImageEventEmitter::onLoad(OnLoad $event) const {
      |                                            ^
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:26: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                          ^
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:32:43: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   32 |   dispatchEvent("load", [$event=std::move($event)](jsi::Runtime &runtime) {
      |                                           ^
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:33:10: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   33 |     auto $payload = jsi::Object(runtime);
      |          ^
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:36:40: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   36 |   source.setProperty(runtime, "width", $event.source.width);
      |                                        ^
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:37:41: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   37 |   source.setProperty(runtime, "height", $event.source.height);
      |                                         ^
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:38:38: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   38 |   source.setProperty(runtime, "uri", $event.source.uri);
      |                                      ^
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:39:3: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   39 |   $payload.setProperty(runtime, "source", source);
      |   ^
C:/Users/<USER>/eclipse-workspace/club-m-app/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp:41:12: warning: '$' in identifier [-Wdollar-in-identifier-extension]
   41 |     return $payload;
      |            ^
9 warnings generated.
[58/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o
[59/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/ab78cb32376308f1452fed2f863dee37/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o
[60/62] Building CXX object rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/dd8aabcb51cd4db9f6adc60e572921e9/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o
[61/62] Linking CXX shared library C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\x86_64\libreact_codegen_rnsvg.so
[62/62] Linking CXX shared library C:\Users\<USER>\eclipse-workspace\club-m-app\android\app\build\intermediates\cxx\Debug\5c14151w\obj\x86_64\libappmodules.so
