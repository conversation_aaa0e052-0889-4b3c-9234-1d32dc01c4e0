import React, {useState} from "react";
import {Text, View, TouchableOpacity, Alert} from "react-native";
import ScreenWithHeader from "../../components/screen-with-header";
import styles from "@/styles/settings/privacy.style";

interface PrivacySettings {
  showLocation: boolean;
  showPhone: boolean;
  showEmail: boolean;
  showCurrentNeeds: boolean;
  showObjectives: boolean;
}

const Privacy: React.FC = () => {
  const [settings, setSettings] = useState<PrivacySettings>({
    showLocation: true,
    showPhone: true,
    showEmail: true,
    showCurrentNeeds: true,
    showObjectives: false
  });

  const updateSetting = (key: keyof PrivacySettings, value: boolean) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value
    }));
  };

  const handleRestoreDefaults = () => {
    Alert.alert(
      "Restaurar configurações padrão",
      "Tem certeza que deseja restaurar todas as configurações de privacidade para os valores padrão?",
      [
        {
          text: "Cancelar",
          style: "cancel"
        },
        {
          text: "Restaurar",
          onPress: () => {
            setSettings({
              showLocation: true,
              showPhone: true,
              showEmail: true,
              showCurrentNeeds: true,
              showObjectives: false
            });
          }
        }
      ]
    );
  };

  const privacyItems: Array<{
    key: keyof PrivacySettings;
    title: string;
    subtitle: string;
  }> = [
    {
      key: "showLocation",
      title: "Localização",
      subtitle: "Balneário Camboriú - SC"
    },
    {key: "showPhone", title: "Telefone", subtitle: "(+55 47 0000-0000)"},
    {key: "showEmail", title: "E-mail", subtitle: "<EMAIL>"},
    {
      key: "showCurrentNeeds",
      title: "Necessidade atual",
      subtitle: "3 (três) atuais"
    },
    {key: "showObjectives", title: "Objetivos", subtitle: "1 (uma) atual"}
  ];

  return (
    <ScreenWithHeader
      screenTitle="Opções de privacidade"
      backButton
      disablePadding
    >
      <ScreenWithHeader.InternalPadding style={styles.contentContainer}>
        <Text style={styles.sectionTitle}>Alterar visibilidade de:</Text>

        {privacyItems.map((item) => (
          <View key={item.key} style={styles.privacyItem}>
            <View>
              <Text style={styles.privacyTitle}>{item.title}</Text>
              <Text style={styles.privacySubtitle}>{item.subtitle}</Text>
            </View>
            <View style={styles.privacyControl}>
              <Text style={styles.privacyStatus}>
                {settings[item.key] ? "Visível" : "Não visível"}
              </Text>
              <View style={styles.toggleContainer}>
                <TouchableOpacity
                  style={[
                    styles.toggleBase,
                    settings[item.key]
                      ? styles.toggleEnabled
                      : styles.toggleDisabled
                  ]}
                  onPress={() => updateSetting(item.key, !settings[item.key])}
                  activeOpacity={0.8}
                >
                  <View
                    style={[
                      styles.toggleButton,
                      settings[item.key]
                        ? styles.toggleButtonEnabled
                        : styles.toggleButtonDisabled
                    ]}
                  />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        ))}

        <TouchableOpacity
          style={styles.restoreButton}
          onPress={handleRestoreDefaults}
        >
          <Text style={styles.restoreButtonText}>
            Restaurar configurações padrão
          </Text>
        </TouchableOpacity>
      </ScreenWithHeader.InternalPadding>
    </ScreenWithHeader>
  );
};

export default Privacy;
