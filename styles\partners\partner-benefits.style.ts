import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  header: {
    alignItems: "center",
    paddingVertical: 24,
    paddingHorizontal: 24
  },
  logoContainer: {
    width: 120,
    height: 120,
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    padding: 16
  },
  logo: {
    width: "100%",
    height: "100%"
  },
  logoText: {
    color: stylesConstants.colors.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    textAlign: "center"
  },
  benefitInfo: {
    paddingHorizontal: 24,
    marginBottom: 24
  },
  benefitTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 20,
    fontWeight: "700",
    lineHeight: 28,
    marginBottom: 8
  },
  benefitDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 8
  },
  seeMoreLink: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  acquireSection: {
    paddingHorizontal: 24,
    marginBottom: 32
  },
  validUntil: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    textAlign: "center",
    marginTop: 8
  },
  detailsSection: {
    paddingHorizontal: 24,
    marginBottom: 32
  },
  sectionTitle: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "700",
    lineHeight: 24,
    marginBottom: 16
  },
  sectionSubtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    marginBottom: 12
  },
  benefitItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: stylesConstants.colors.borderDefault
  },
  benefitItemLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    flex: 1
  },
  benefitItemValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  termsLink: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    marginTop: 16,
    marginBottom: 16
  },
  validUntilLabel: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "400",
    lineHeight: 20,
    marginBottom: 4
  },
  validUntilValue: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  paymentSection: {
    paddingHorizontal: 24,
    marginBottom: 32
  },
  paymentMethods: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 12
  },
  paymentMethodIcon: {
    backgroundColor: stylesConstants.colors.fullWhite,
    borderRadius: 4,
    padding: 4
  },
  paymentNote: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },
  socialSection: {
    paddingHorizontal: 24,
    marginBottom: 32
  },
  socialItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    gap: 12
  },
  socialText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20,
    flex: 1
  },
  socialUrl: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18
  },
  buyNowSection: {
    paddingHorizontal: 24,
    paddingBottom: 32
  }
});

export default styles;
