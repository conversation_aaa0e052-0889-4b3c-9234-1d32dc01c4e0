import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },
  contentContainer: {
    gap: 24
  },
  sectionTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24,
    marginBottom: 0,
    opacity: 0.8
  },
  privacyItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 0,
    gap: 16
  },
  privacyTitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: "600",
    lineHeight: 20
  },
  privacySubtitle: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: "400",
    lineHeight: 18,
    opacity: 0.8,
    marginTop: 2
  },
  privacyControl: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8
  },
  privacyStatus: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 10,
    fontWeight: "400",
    lineHeight: 18
  },
  toggleContainer: {
    flexDirection: "column",
    alignItems: "flex-start",
    gap: 10
  },
  toggleBase: {
    flexDirection: "row",
    alignItems: "center",
    padding: 2,
    width: 44,
    height: 24,
    borderRadius: 12
  },
  toggleEnabled: {
    backgroundColor: "#1F9464",
    justifyContent: "flex-end"
  },
  toggleDisabled: {
    backgroundColor: "#D0D5DD",
    justifyContent: "flex-start"
  },
  toggleButton: {
    width: 20,
    height: 20,
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    shadowColor: "rgba(16, 24, 40, 0.1)",
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 1,
    shadowRadius: 3,
    elevation: 2
  },
  toggleButtonEnabled: {
    // Position is handled by justifyContent in toggleBase
  },
  toggleButtonDisabled: {
    // Position is handled by justifyContent in toggleBase
  },
  restoreButton: {
    backgroundColor: "transparent",
    paddingVertical: 16,
    paddingHorizontal: 0,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 24
  },
  restoreButtonText: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    fontWeight: "400",
    lineHeight: 24
  }
});

export default styles;
